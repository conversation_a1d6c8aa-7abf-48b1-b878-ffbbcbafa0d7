'use client';
import { Col, Row } from "react-bootstrap";
import React, { useState, useEffect, useCallback } from 'react'
import AccountLayout from "@/Layouts/AccountLayout";
import MetaHead from "@/Seo/Meta/MetaHead";
import SidebarHeading from "@/Components/common/Account/SidebarHeading";
import TextInput from "@/Components/UI/TextInput";
import StatusIndicator from "@/Components/UI/StatusIndicator";
import { useSelector, useDispatch } from 'react-redux';
import { setUser } from '@/redux/authSlice';
import { useRouter, useSearchParams } from 'next/navigation';
import { get, updateUsername, checkUsernameAvailability } from '@/utils/apiUtils';
import debounce from 'lodash.debounce';
import "@/css/account/AccountDetails.scss";

export default function CreateUsername() {
    const [username, setUsername] = useState('');
    const [originalUsername, setOriginalUsername] = useState('');
    const [userData, setUserData] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    const [isLoadingUserData, setIsLoadingUserData] = useState(true);
    const [saveStatus, setSaveStatus] = useState(null); // 'loading', 'success', 'error', null
    const [error, setError] = useState(null);

    // Validation state
    const [validationError, setValidationError] = useState(null);
    const [availabilityStatus, setAvailabilityStatus] = useState(null); // 'checking', 'available', 'taken', null
    const [isTouched, setIsTouched] = useState(false);

    const router = useRouter();
    const searchParams = useSearchParams();
    const reduxUser = useSelector((state) => state?.auth?.user || null);
    const dispatch = useDispatch();

    const metaArray = {
        noindex: true,
        title: "Create Username | TradeReply",
        description: "Create or update your username on TradeReply.com.",
        canonical_link: "https://www.tradereply.com/account/username/setup",
        og_site_name: "TradeReply",
        og_title: "Create Username | TradeReply",
        og_description: "Create or update your username on TradeReply.com.",
        twitter_title: "Create Username | TradeReply",
        twitter_description: "Create or update your username on TradeReply.com.",
    };

    // Username validation function
    const validateUsername = (usernameValue) => {
        if (!usernameValue || !usernameValue.trim()) {
            return "Username is required";
        }

        const trimmedUsername = usernameValue.trim();

        // Length validation
        if (trimmedUsername.length < 3) {
            return "Username must be at least 3 characters";
        }
        if (trimmedUsername.length > 20) {
            return "Username cannot exceed 20 characters";
        }

        // Format validation - must start with letter, can contain letters, numbers, underscores
        if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(trimmedUsername)) {
            return "Username must start with a letter and can only contain letters, numbers, and underscores";
        }

        return null; // No validation errors
    };

    // Fetch user data to pre-populate username and check limits
    const fetchUserData = async () => {
        try {
            setIsLoadingUserData(true);
            const response = await get('/account');

            if (response.success && response.data) {
                setUserData(response.data);
                const existingUsername = response.data.username || '';
                setUsername(existingUsername);
                setOriginalUsername(existingUsername);

                // Update Redux store
                dispatch(setUser(response.data));
                localStorage.setItem('user', JSON.stringify(response.data));
            }
        } catch (error) {
            console.error('Error fetching user data:', error);
            // Fallback to Redux user data
            if (reduxUser) {
                setUserData(reduxUser);
                const existingUsername = reduxUser.username || '';
                setUsername(existingUsername);
                setOriginalUsername(existingUsername);
            }
        } finally {
            setIsLoadingUserData(false);
        }
    };

    useEffect(() => {
        fetchUserData();
    }, []);

    // Get changes remaining
    const getChangesRemaining = () => {
        if (!userData) return 2;
        const changesUsed = userData.username_change_count || 0;
        return Math.max(0, 2 - changesUsed);
    };

    const hasChangesRemaining = () => {
        return getChangesRemaining() > 0;
    };

    const isUpdating = originalUsername.length > 0;

    // Debounced username availability check
    const checkAvailability = useCallback(
        debounce(async (usernameValue) => {
            if (!usernameValue || usernameValue === originalUsername) {
                setAvailabilityStatus(null);
                return;
            }

            const validationErr = validateUsername(usernameValue);
            if (validationErr) {
                setAvailabilityStatus(null);
                return;
            }

            try {
                setAvailabilityStatus('checking');
                const response = await checkUsernameAvailability(usernameValue);

                if (response.success) {
                    setAvailabilityStatus('available');
                } else {
                    setAvailabilityStatus('taken');
                }
            } catch (error) {
                console.error('Error checking username availability:', error);
                setAvailabilityStatus('taken');
            }
        }, 500),
        [originalUsername]
    );

    // Handle username input change
    const handleUsernameChange = (e) => {
        const value = e.target.value;

        // Only allow letters, numbers, and underscores
        const filteredValue = value.replace(/[^a-zA-Z0-9_]/g, '');

        setUsername(filteredValue);
        setIsTouched(true);

        const validationErr = validateUsername(filteredValue);
        setValidationError(validationErr);

        // Clear save status when user types
        if (saveStatus) {
            setSaveStatus(null);
            setError(null);
        }

        // Check availability if validation passes
        if (!validationErr && filteredValue !== originalUsername) {
            checkAvailability(filteredValue);
        } else {
            setAvailabilityStatus(null);
        }
    };

    // Get redirect URL (similar to phone setup pattern)
    const getRedirectUrl = () => {
        // Check URL parameters first
        const from = searchParams.get('from');
        if (from) {
            try {
                return decodeURIComponent(from);
            } catch (e) {
                console.warn('Failed to decode from parameter:', from);
            }
        }

        // Check document referrer as fallback
        if (typeof window !== 'undefined' && document.referrer) {
            try {
                const referrerUrl = new URL(document.referrer);
                // Only use referrer if it's from the same origin
                if (referrerUrl.origin === window.location.origin) {
                    return referrerUrl.pathname + referrerUrl.search;
                }
            } catch (e) {
                console.warn('Failed to parse document referrer:', document.referrer);
            }
        }

        // Default fallback
        return '/account/details';
    };

    // Handle save username
    const handleSave = async () => {
        // Mark field as touched for validation display
        setIsTouched(true);

        // Validate username before submission
        const validationErr = validateUsername(username);
        if (validationErr) {
            setValidationError(validationErr);
            return;
        }

        // Check if username has changed
        if (username === originalUsername) {
            setError('Please enter a different username to update.');
            return;
        }

        // Check if user has changes remaining
        if (!hasChangesRemaining()) {
            setError('You have used all available username changes.');
            return;
        }

        try {
            setIsLoading(true);
            setSaveStatus('loading');
            setError(null);
            setValidationError(null);

            const response = await updateUsername(username);

            if (response.success) {
                setSaveStatus('success');

                // Update local state with fresh user data
                if (response.data.user) {
                    setUserData(response.data.user);
                    dispatch(setUser(response.data.user));
                    localStorage.setItem('user', JSON.stringify(response.data.user));
                }

                // Get the appropriate redirect URL
                const redirectUrl = getRedirectUrl();
                console.log('Username update success - redirecting to:', redirectUrl);

                // Redirect back to the original page after 2 seconds
                setTimeout(() => {
                    router.push(redirectUrl);
                }, 2000);
            } else {
                throw new Error(response.message || 'Failed to update username');
            }
        } catch (err) {
            console.error('Username update error:', err);
            const errorMessage = err.response?.data?.message ||
                                err.response?.data?.errors?.username?.[0] ||
                                err.message ||
                                'Failed to update username. Please try again.';
            setSaveStatus('error');
            setError(errorMessage);
        } finally {
            setIsLoading(false);
            setTimeout(() => setSaveStatus(null), 3000);
        }
    };

    // Handle cancel
    const handleCancel = () => {
        // Reset validation state
        setValidationError(null);
        setIsTouched(false);
        setError(null);
        setSaveStatus(null);
        setAvailabilityStatus(null);

        const redirectUrl = getRedirectUrl();
        router.push(redirectUrl);
    };

    // Check if user has reached change limit
    const changesRemaining = getChangesRemaining();
    const hasReachedLimit = !hasChangesRemaining();

    return (
        <>
            <AccountLayout>
                <MetaHead props={metaArray} />
                <div className="account_create_username">
                    <SidebarHeading title={isUpdating ? "Update Username" : "Create Username"} />
                    <div className="mb-3 mb-lg-4">
                        <div className="common_blackcard account_card">
                            <div className="common_blackcard_innerheader">
                                <div className="common_blackcard_innerheader_content">
                                    <div className="account_header_main justify-content-start">
                                        <div>
                                            <h6>Username</h6>
                                        </div>
                                        <div className="account_status_indicator">
                                            <StatusIndicator
                                                saveStatus={saveStatus}
                                                error={error}
                                                successText="Saved"
                                                defaultText="Not saved"
                                            />
                                        </div>
                                    </div>
                                    <p>
                                        {hasReachedLimit
                                            ? "You have used all available username changes. Contact support if you need additional changes."
                                            : `Choose a unique username for your TradeReply account. You have ${changesRemaining} change${changesRemaining !== 1 ? 's' : ''} remaining.`
                                        }
                                    </p>
                                </div>
                            </div>
                            <div className="common_blackcard_innerbody">
                                <div className="account_card_list">
                                    <Row>
                                        <Col lg={6}>
                                            <div className="account_card_list_item">
                                                <label>Username</label>
                                                <TextInput
                                                    type="text"
                                                    placeholder="Enter your username"
                                                    value={username}
                                                    onChange={handleUsernameChange}
                                                    disabled={isLoading || isLoadingUserData || hasReachedLimit}
                                                    maxLength={20}
                                                />
                                                <small className="form-text text-muted">
                                                    {/* Validation errors take priority */}
                                                    {validationError && isTouched && (
                                                        <div className="mt-1">
                                                            <p style={{ color: 'red', fontSize: '14px' }}>
                                                                {validationError}
                                                            </p>
                                                        </div>
                                                    )}

                                                    {/* Availability status */}
                                                    {!validationError && availabilityStatus && (
                                                        <div className="mt-1">
                                                            {availabilityStatus === 'checking' && (
                                                                <p style={{ color: '#007bff', fontSize: '14px' }}>
                                                                    Checking availability...
                                                                </p>
                                                            )}
                                                            {availabilityStatus === 'available' && (
                                                                <p style={{ color: 'green', fontSize: '14px' }}>
                                                                    ✓ Username is available
                                                                </p>
                                                            )}
                                                            {availabilityStatus === 'taken' && (
                                                                <p style={{ color: 'red', fontSize: '14px' }}>
                                                                    ✗ Username is already taken
                                                                </p>
                                                            )}
                                                        </div>
                                                    )}

                                                    {/* Show save errors only when no validation errors */}
                                                    {!validationError && error && saveStatus !== 'loading' && (
                                                        <div className="mt-1">
                                                            <p style={{ color: 'red', fontSize: '14px' }}>
                                                                {error}
                                                            </p>
                                                        </div>
                                                    )}
                                                </small>
                                            </div>
                                        </Col>
                                    </Row>

                                    <div className="col-12 mt-3">
                                        <div className="username-rules">
                                            <h6>Username Requirements:</h6>
                                            <ul>
                                                <li>Must be between 3-20 characters</li>
                                                <li>Can contain letters, numbers, and underscores</li>
                                                <li>Must start with a letter</li>
                                                <li>Cannot contain spaces or special characters</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="account_card_list_btns mt-3">
                            <button
                                className="btn-style white-btn"
                                onClick={handleCancel}
                                disabled={isLoading || isLoadingUserData}
                            >
                                Cancel
                            </button>
                            <button
                                className="btn-style"
                                onClick={handleSave}
                                disabled={
                                    isLoading ||
                                    isLoadingUserData ||
                                    !username.trim() ||
                                    (isTouched && validationError) ||
                                    availabilityStatus === 'taken' ||
                                    availabilityStatus === 'checking' ||
                                    username === originalUsername ||
                                    hasReachedLimit
                                }
                            >
                                {isLoading ? "Saving..." : isUpdating ? "Update Username" : "Save Username"}
                            </button>
                        </div>
                    </div>
                </div>
            </AccountLayout>
        </>
    )
}
