/**
 * Security Routes Configuration
 *
 * This file contains the security-protected routes configuration that is used
 * by the Next.js middleware and other frontend components. This configuration
 * is self-contained within the frontend to avoid dependencies on backend APIs.
 *
 * To update this configuration:
 * 1. Modify the constants below directly in this file
 * 2. Update the corresponding backend config/security.php if needed for backend middleware
 * 3. Redeploy the frontend
 *
 * Note: This is the single source of truth for frontend security route configuration.
 */

/**
 * Security-protected routes that require verification
 * Add new routes here when implementing new secure pages
 */
export const SECURITY_PROTECTED_ROUTES = [
  '/account/phone/setup',
  // '/account/email/setup',
  // '/account/password/change',
  // '/account/2fa/setup',
  '/account/username/setup',
  // '/account/address/manage',
  // '/account/address/setup',
];

/**
 * Auth flow protected routes that require proper signup flow navigation
 * These routes should only be accessible through the legitimate signup process
 */
export const AUTH_FLOW_PROTECTED_ROUTES = [
  '/create-username',
];

/**
 * Valid referrer path prefixes (pages users can navigate from)
 * These are the application pages that users can legitimately navigate from to secure pages
 */
export const VALID_REFERRER_PREFIXES = [
  '/account',
  '/dashboard',
  '/user',
  '/marketplace',
  '/pricing',
  '/help',
  '/settings',
  '/', // Home page
];

/**
 * Invalid referrer paths (pages users should not navigate from)
 * These are pages that should not be able to directly link to secure pages
 */
export const INVALID_REFERRER_PATHS = [
  '/login',
  '/signup',
  '/forget-password',
  '/verify-email',
  '/security-check',
  '/logout',
];

/**
 * Fallback URL mappings for direct access prevention
 * When users try to access secure pages directly, they are redirected to these fallback URLs
 */
export const FALLBACK_URL_MAPPINGS = {
  '/account/phone/setup': '/account/overview',
  // '/account/email/setup': '/account/overview',
  // '/account/password/change': '/account/overview',
  // '/account/2fa/setup': '/account/overview',
  '/account/create-username': '/account/overview',
  // '/account/address/manage': '/account/overview',
  // '/account/address/setup': '/account/address/manage',
};

/**
 * Default fallback URL when no specific mapping exists
 */
export const DEFAULT_FALLBACK_URL = '/account/overview';

/**
 * Valid referrer prefixes for security-check page access
 * These are the pages that can legitimately redirect users to the security-check page
 */
export const VALID_SECURITY_CHECK_REFERRERS = [
  '/account',
  '/dashboard',
  '/user'
];

/**
 * Valid referrer prefixes for auth flow protected routes
 * These are the pages that can legitimately redirect users to auth flow pages like /create-username
 */
export const VALID_AUTH_FLOW_REFERRERS = [
  '/security-check',
];

/**
 * Security configuration settings
 */
export const SECURITY_CONFIG = {
  // Enable referrer-based access control
  referrerControlEnabled: true,

  // Cookie configuration for client-side validation
  cookie: {
    name: 'security_verified',
    // Note: Actual expiration is controlled by backend, this is for client-side validation only
    defaultExpirationMinutes: 5,
    checkIntervalSeconds: 30,
  },
};

import { getBaseUrlForParsing } from '@/utils/environment';

/**
 * Helper function to validate if a path is a valid secure route
 * @param {string} path - The path to validate
 * @returns {boolean} True if the path is a valid secure route
 */
export function isValidSecureRoute(path) {
  try {
    // Decode URL if it's encoded
    const decodedPath = decodeURIComponent(path);

    // For relative paths, extract path directly without URL constructor
    let pathOnly;
    if (decodedPath.startsWith('/')) {
      // It's already a relative path, use it directly
      pathOnly = decodedPath.split('?')[0]; // Remove query parameters
    } else {
      // It might be an absolute URL, use URL constructor with dynamic base
      const baseUrl = getBaseUrlForParsing();
      const url = new URL(decodedPath, baseUrl);
      pathOnly = url.pathname;
    }

    // Check if the path exactly matches any of the protected routes
    // or if it starts with any of the protected routes (for sub-paths)
    return SECURITY_PROTECTED_ROUTES.some(route => {
      // Exact match
      if (pathOnly === route) {
        return true;
      }

      // Check if path starts with the route (for sub-paths like /account/phone/setup/step2)
      if (pathOnly.startsWith(route + '/')) {
        return true;
      }

      return false;
    });
  } catch (error) {
    console.warn('Error validating secure route:', error);
    return false;
  }
}

/**
 * Helper function to get appropriate fallback URL for direct access prevention
 * @param {string} securePagePath - The secure page path
 * @returns {string} Fallback URL
 */
export function getDirectAccessFallbackUrl(securePagePath) {
  return FALLBACK_URL_MAPPINGS[securePagePath] || DEFAULT_FALLBACK_URL;
}

/**
 * Helper function to validate if a path is a valid auth flow route
 * @param {string} path - The path to validate
 * @returns {boolean} True if the path is a valid auth flow route
 */
export function isValidAuthFlowRoute(path) {
  try {
    // Decode URL if it's encoded
    const decodedPath = decodeURIComponent(path);

    // For relative paths, extract path directly without URL constructor
    let pathOnly;
    if (decodedPath.startsWith('/')) {
      // It's already a relative path, use it directly
      pathOnly = decodedPath.split('?')[0]; // Remove query parameters
    } else {
      // It might be an absolute URL, use URL constructor with dynamic base
      const baseUrl = getBaseUrlForParsing();
      const url = new URL(decodedPath, baseUrl);
      pathOnly = url.pathname;
    }

    // Check if the path exactly matches any of the auth flow protected routes
    return AUTH_FLOW_PROTECTED_ROUTES.some(route => {
      // Exact match
      if (pathOnly === route) {
        return true;
      }

      // Check if path starts with the route (for sub-paths)
      if (pathOnly.startsWith(route + '/')) {
        return true;
      }

      return false;
    });
  } catch (error) {
    console.warn('Error validating auth flow route:', error);
    return false;
  }
}

/**
 * Get the appropriate fallback URL for auth flow direct access prevention
 * @param {string} attemptedPath - The path the user tried to access directly
 * @returns {string} - The fallback URL to redirect to
 */
export function getAuthFlowFallbackUrl(attemptedPath) {
  // Map specific auth flow routes to their appropriate fallback pages
  const authFlowFallbacks = {
    '/create-username': '/signup',
    // Add more auth flow fallbacks as needed
  };

  // Check for specific route fallback first
  if (authFlowFallbacks[attemptedPath]) {
    return authFlowFallbacks[attemptedPath];
  }

  // Default fallback for auth flow routes
  return '/signup';
}

/**
 * Configuration metadata
 */
export const CONFIG_METADATA = {
  lastSynced: new Date().toISOString(),
  source: 'backend:config/security.php',
  version: '1.0.0'
};
